<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Modules\Cotisations\Models\Cotisation;
use Modules\Adherents\Models\Adherent;
use Carbon\Carbon;

class CotisationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adherents = Adherent::all();

        if ($adherents->isEmpty()) {
            $this->command->warn('Aucun adhérent trouvé. Veuillez d\'abord exécuter AdherentSeeder.');
            return;
        }

        $mois = ['janvier', 'fevrier', 'mars', 'avril', 'mai', 'juin',
                 'juillet', 'aout', 'septembre', 'octobre', 'novembre', 'decembre'];

        $types = ['mensuelle', 'trimestrielle', 'semestrielle', 'annuelle'];
        $statuts = ['en_attente', 'payee', 'en_retard'];

        foreach ($adherents as $adherent) {
            // Créer des cotisations pour les 12 derniers mois
            for ($i = 0; $i < 12; $i++) {
                $date = Carbon::now()->subMonths($i);
                $moisIndex = $date->month - 1;

                // Calculer le montant basé sur le salaire de l'adhérent (taux français)
                $montant = round($adherent->salaire_base * 0.1083); // 10,83% du salaire (retraite de base française)

                // Déterminer le statut (80% payées, 15% en attente, 5% en retard)
                $rand = rand(1, 100);
                if ($rand <= 80) {
                    $statut = 'payee';
                    $datePaiement = $date->copy()->addDays(rand(1, 15));
                    $modePaiement = ['especes', 'cheque', 'virement', 'mobile_money'][rand(0, 3)];
                } elseif ($rand <= 95) {
                    $statut = 'en_attente';
                    $datePaiement = null;
                    $modePaiement = null;
                } else {
                    $statut = 'en_retard';
                    $datePaiement = null;
                    $modePaiement = null;
                }

                Cotisation::create([
                    'numero_cotisation' => 'COT' . $date->format('Y') . str_pad($adherent->id, 3, '0', STR_PAD_LEFT) . str_pad($moisIndex + 1, 2, '0', STR_PAD_LEFT),
                    'adherent_id' => $adherent->id,
                    'periode_cotisation' => $mois[$moisIndex],
                    'annee_cotisation' => $date->year,
                    'mois_cotisation' => $date->month,
                    'montant_base' => $adherent->salaire_base,
                    'taux_cotisation' => 0.1083, // 10,83% en décimal (taux français)
                    'montant_cotisation' => $montant,
                    'montant_employeur' => round($montant * 0.6), // 60% employeur
                    'montant_employe' => round($montant * 0.4), // 40% employé
                    'date_echeance' => $date->copy()->endOfMonth(),
                    'statut' => $statut,
                    'date_paiement' => $datePaiement,
                    'mode_paiement' => $modePaiement,
                    'reference_paiement' => $statut === 'payee' ? 'REF' . rand(100000, 999999) : null,
                    'observations' => $statut === 'en_retard' ? 'Cotisation en retard' : null,
                    'created_by' => 1, // Admin user
                ]);
            }
        }

        $this->command->info('Cotisations créées avec succès pour tous les adhérents.');
    }
}
