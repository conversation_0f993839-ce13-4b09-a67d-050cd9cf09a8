<?php

namespace Modules\Dashboard\Services;

use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class DashboardService
{
    /**
     * Get quick statistics for dashboard
     */
    public function getQuickStats(): array
    {
        return Cache::remember('dashboard.quick_stats', 300, function () {
            return [
                'total_adherents' => $this->getTotalAdherents(),
                'cotisations_mois' => $this->getCotisationsMois(),
                'pensions_en_cours' => $this->getPensionsEnCours(),
                'utilisateurs_actifs' => $this->getUtilisateursActifs(),
            ];
        });
    }

    /**
     * Get recent activities
     */
    public function getRecentActivities(): array
    {
        return Cache::remember('dashboard.recent_activities', 300, function () {
            // Pour l'instant, retournons des données simulées
            // Ces données seront remplacées par de vraies données des autres modules
            return [
                [
                    'type' => 'cotisation',
                    'message' => 'Nouvelle cotisation enregistrée pour <PERSON>',
                    'time' => now()->subMinutes(15),
                    'icon' => 'feather icon-dollar-sign',
                    'color' => 'success'
                ],
                [
                    'type' => 'pension',
                    'message' => 'Dossier de pension validé pour Marie Martin',
                    'time' => now()->subHours(2),
                    'icon' => 'feather icon-check-circle',
                    'color' => 'info'
                ],
                [
                    'type' => 'adherent',
                    'message' => 'Nouvel adhérent inscrit : Pierre Kouassi',
                    'time' => now()->subHours(4),
                    'icon' => 'feather icon-user-plus',
                    'color' => 'primary'
                ],
                [
                    'type' => 'rapport',
                    'message' => 'Rapport mensuel généré avec succès',
                    'time' => now()->subDay(),
                    'icon' => 'feather icon-file-text',
                    'color' => 'warning'
                ]
            ];
        });
    }

    /**
     * Get chart data
     */
    public function getChartData(string $type = 'cotisations', string $period = 'month'): array
    {
        $cacheKey = "dashboard.chart_data.{$type}.{$period}";
        
        return Cache::remember($cacheKey, 300, function () use ($type, $period) {
            // Données simulées pour les graphiques
            // Ces données seront remplacées par de vraies données des autres modules
            
            $labels = $this->getLabelsForPeriod($period);
            
            return match($type) {
                'cotisations' => [
                    'labels' => $labels,
                    'datasets' => [
                        [
                            'label' => 'Cotisations (FCFA)',
                            'data' => [1200000, 1350000, 1100000, 1450000, 1600000, 1300000],
                            'backgroundColor' => 'rgba(26, 35, 126, 0.1)',
                            'borderColor' => 'rgba(26, 35, 126, 1)',
                            'borderWidth' => 2,
                            'fill' => true
                        ]
                    ]
                ],
                'pensions' => [
                    'labels' => $labels,
                    'datasets' => [
                        [
                            'label' => 'Pensions versées (FCFA)',
                            'data' => [800000, 850000, 900000, 950000, 1000000, 1050000],
                            'backgroundColor' => 'rgba(40, 53, 147, 0.1)',
                            'borderColor' => 'rgba(40, 53, 147, 1)',
                            'borderWidth' => 2,
                            'fill' => true
                        ]
                    ]
                ],
                'adherents' => [
                    'labels' => $labels,
                    'datasets' => [
                        [
                            'label' => 'Nouveaux adhérents',
                            'data' => [25, 30, 22, 35, 40, 28],
                            'backgroundColor' => 'rgba(76, 175, 80, 0.1)',
                            'borderColor' => 'rgba(76, 175, 80, 1)',
                            'borderWidth' => 2,
                            'fill' => true
                        ]
                    ]
                ],
                default => []
            };
        });
    }

    /**
     * Get pending tasks
     */
    public function getPendingTasks(): array
    {
        return Cache::remember('dashboard.pending_tasks', 300, function () {
            // Données simulées pour les tâches en attente
            return [
                [
                    'title' => 'Validation des cotisations de janvier',
                    'priority' => 'high',
                    'due_date' => now()->addDays(2),
                    'assigned_to' => 'Équipe Cotisations'
                ],
                [
                    'title' => 'Traitement des dossiers de pension',
                    'priority' => 'medium',
                    'due_date' => now()->addWeek(),
                    'assigned_to' => 'Équipe Pensions'
                ],
                [
                    'title' => 'Mise à jour des données adhérents',
                    'priority' => 'low',
                    'due_date' => now()->addWeeks(2),
                    'assigned_to' => 'Équipe Adhérents'
                ]
            ];
        });
    }

    /**
     * Get notifications
     */
    public function getNotifications(): array
    {
        return Cache::remember('dashboard.notifications', 300, function () {
            return [
                [
                    'title' => 'Rappel de cotisation',
                    'message' => '15 adhérents ont des cotisations en retard',
                    'type' => 'warning',
                    'time' => now()->subHours(1)
                ],
                [
                    'title' => 'Nouveau dossier de pension',
                    'message' => '3 nouveaux dossiers à traiter',
                    'type' => 'info',
                    'time' => now()->subHours(3)
                ]
            ];
        });
    }

    /**
     * Get total adherents (simulated)
     */
    private function getTotalAdherents(): int
    {
        // Sera remplacé par la vraie logique du module Adhérents
        return 1250;
    }

    /**
     * Get cotisations for current month (simulated)
     */
    private function getCotisationsMois(): int
    {
        // Sera remplacé par la vraie logique du module Cotisations
        return 1450000;
    }

    /**
     * Get pensions en cours (simulated)
     */
    private function getPensionsEnCours(): int
    {
        // Sera remplacé par la vraie logique du module Pensions
        return 85;
    }

    /**
     * Get active users
     */
    private function getUtilisateursActifs(): int
    {
        return User::active()->count();
    }

    /**
     * Get labels for chart period
     */
    private function getLabelsForPeriod(string $period): array
    {
        return match($period) {
            'week' => ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
            'month' => ['Sem 1', 'Sem 2', 'Sem 3', 'Sem 4', 'Sem 5', 'Sem 6'],
            'year' => ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
            default => ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun']
        };
    }
}
