<?php

namespace Modules\Dashboard;

use App\Providers\BaseModuleServiceProvider;

class DashboardServiceProvider extends BaseModuleServiceProvider
{
    /**
     * Get module name
     */
    protected function getModuleName(): string
    {
        return "Dashboard";
    }

    /**
     * Register module services
     */
    protected function registerServices(): void
    {
        // Register Dashboard service
        $this->app->bind(\Modules\Dashboard\Services\DashboardService::class);
    }
}