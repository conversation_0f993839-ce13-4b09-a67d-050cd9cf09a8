<?php

use Illuminate\Support\Facades\Route;
use Modules\Pensions\Controllers\PensionController;

/*
|--------------------------------------------------------------------------
| Pensions Module Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['web', 'auth'])->group(function () {

    // Routes requiring view permission
    Route::middleware('permission:view-pensions')->group(function () {
        Route::get('/pensions', [PensionController::class, 'index'])->name('pensions.index');
        Route::get('/pensions/{dossier}', [PensionController::class, 'show'])->name('pensions.show');
        Route::get('/pensions/export', [PensionController::class, 'export'])->name('pensions.export');
        Route::get('/pensions/ready-for-liquidation/list', [PensionController::class, 'readyForLiquidation'])->name('pensions.ready-for-liquidation');
        Route::get('/api/pensions/dashboard-stats', [PensionController::class, 'getDashboardStats'])->name('pensions.api.dashboard-stats');

        // Simulation
        Route::get('/pensions/simulation/form', [PensionController::class, 'showSimulation'])->name('pensions.simulation.form');
        Route::post('/pensions/simulation/calculate', [PensionController::class, 'simulate'])->name('pensions.simulation.calculate');
    });

    // Routes requiring manage permission
    Route::middleware('permission:manage-pensions')->group(function () {
        Route::get('/pensions/create', [PensionController::class, 'create'])->name('pensions.create');
        Route::post('/pensions', [PensionController::class, 'store'])->name('pensions.store');
        Route::get('/pensions/{dossier}/edit', [PensionController::class, 'edit'])->name('pensions.edit');
        Route::put('/pensions/{dossier}', [PensionController::class, 'update'])->name('pensions.update');
        Route::delete('/pensions/{dossier}', [PensionController::class, 'destroy'])->name('pensions.destroy');

        // Validation and liquidation
        Route::patch('/pensions/{dossier}/validate', [PensionController::class, 'validateDossier'])->name('pensions.validate');
        Route::patch('/pensions/{dossier}/liquidate', [PensionController::class, 'liquidate'])->name('pensions.liquidate');

        // Pre-liquidation
        Route::get('/pensions/{dossier}/pre-liquidation', [PensionController::class, 'showPreLiquidation'])->name('pensions.pre-liquidation.form');
        Route::post('/pensions/{dossier}/pre-liquidation', [PensionController::class, 'createPreLiquidation'])->name('pensions.pre-liquidation.create');
    });

});