<?php

namespace Modules\Pensions\Services;

use Modules\Pensions\Models\DossierPension;
use Modules\Pensions\Models\PreLiquidation;
use Modules\Pensions\Models\WorkflowStep;
use Modules\Pensions\Repositories\DossierPensionRepository;
use Modules\Adherents\Models\Adherent;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

class PensionService
{
    protected DossierPensionRepository $dossierRepository;

    public function __construct(DossierPensionRepository $dossierRepository)
    {
        $this->dossierRepository = $dossierRepository;
    }

    /**
     * Create new pension dossier
     */
    public function createDossier(array $data): DossierPension
    {
        DB::beginTransaction();
        
        try {
            // Generate dossier number if not provided
            if (empty($data['numero_dossier'])) {
                $data['numero_dossier'] = $this->dossierRepository->generateNextNumero();
            }

            // Set created_by
            $data['created_by'] = Auth::id();

            // Create dossier
            $dossier = $this->dossierRepository->create($data);

            // Calculate initial pension amount
            $this->calculatePensionAmount($dossier);

            // Create workflow steps
            $this->createWorkflowSteps($dossier);

            DB::commit();

            Log::info('Pension dossier created', [
                'dossier_id' => $dossier->id,
                'numero_dossier' => $dossier->numero_dossier,
                'adherent_id' => $dossier->adherent_id,
                'created_by' => Auth::id()
            ]);

            return $dossier;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating pension dossier', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Update pension dossier
     */
    public function updateDossier(DossierPension $dossier, array $data): bool
    {
        DB::beginTransaction();
        
        try {
            $result = $this->dossierRepository->update($dossier, $data);

            // Recalculate pension if relevant data changed
            if (isset($data['salaire_reference']) || isset($data['duree_cotisation_mois'])) {
                $this->calculatePensionAmount($dossier);
            }

            DB::commit();

            if ($result) {
                Log::info('Pension dossier updated', [
                    'dossier_id' => $dossier->id,
                    'numero_dossier' => $dossier->numero_dossier,
                    'updated_by' => Auth::id()
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error updating pension dossier', [
                'dossier_id' => $dossier->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Calculate pension amount for dossier
     */
    public function calculatePensionAmount(DossierPension $dossier): void
    {
        // Get adherent's cotisation data
        $adherent = $dossier->adherent;
        $cotisations = $adherent->cotisations()->paid()->get();

        if ($cotisations->isEmpty()) {
            return;
        }

        // Calculate reference salary (average of last 5 years or all cotisations)
        $referenceSalary = $cotisations->sortByDesc('periode_cotisation')
                                      ->take(60) // Last 5 years (60 months)
                                      ->avg('montant_base');

        // Calculate contribution duration in months
        $contributionMonths = $cotisations->count();

        // Update dossier with calculated values
        $dossier->update([
            'salaire_reference' => $referenceSalary,
            'duree_cotisation_mois' => $contributionMonths,
        ]);

        // Calculate pension amount
        $dossier->calculatePensionAmount();
        $dossier->save();
    }

    /**
     * Create pre-liquidation for dossier
     */
    public function createPreLiquidation(DossierPension $dossier, array $data = []): PreLiquidation
    {
        DB::beginTransaction();
        
        try {
            $preLiquidationData = array_merge([
                'dossier_pension_id' => $dossier->id,
                'adherent_id' => $dossier->adherent_id,
                'numero_pre_liquidation' => PreLiquidation::generateNextNumero(),
                'date_calcul' => now(),
                'salaire_reference' => $dossier->salaire_reference,
                'duree_cotisation_mois' => $dossier->duree_cotisation_mois,
                'taux_liquidation' => $dossier->taux_liquidation,
                'montant_brut' => $dossier->montant_pension_calcule,
                'date_effet' => $dossier->date_depart_retraite,
                'calculated_by' => Auth::id(),
                'statut' => PreLiquidation::STATUS_CALCULE,
            ], $data);

            $preLiquidation = $dossier->preLiquidation()->create($preLiquidationData);

            // Calculate retenues and net amount
            $preLiquidation->calculateRetenues();
            $preLiquidation->save();

            DB::commit();

            Log::info('Pre-liquidation created', [
                'pre_liquidation_id' => $preLiquidation->id,
                'dossier_id' => $dossier->id,
                'calculated_by' => Auth::id()
            ]);

            return $preLiquidation;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error creating pre-liquidation', [
                'dossier_id' => $dossier->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Validate dossier
     */
    public function validateDossier(DossierPension $dossier): bool
    {
        try {
            $dossier->markAsValidated(Auth::id());

            // Move to next workflow step
            $this->advanceWorkflow($dossier, 'validation_technique');

            Log::info('Pension dossier validated', [
                'dossier_id' => $dossier->id,
                'validated_by' => Auth::id()
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Error validating pension dossier', [
                'dossier_id' => $dossier->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Liquidate dossier
     */
    public function liquidateDossier(DossierPension $dossier, float $finalAmount = null): bool
    {
        DB::beginTransaction();
        
        try {
            $dossier->markAsLiquidated(Auth::id(), $finalAmount);

            // Complete workflow
            $this->advanceWorkflow($dossier, 'liquidation');

            DB::commit();

            Log::info('Pension dossier liquidated', [
                'dossier_id' => $dossier->id,
                'final_amount' => $finalAmount ?? $dossier->montant_pension_calcule,
                'liquidated_by' => Auth::id()
            ]);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error liquidating pension dossier', [
                'dossier_id' => $dossier->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get statistics
     */
    public function getStatistics(): array
    {
        return $this->dossierRepository->getStatistics();
    }

    /**
     * Get dossiers ready for liquidation
     */
    public function getDossiersReadyForLiquidation()
    {
        return $this->dossierRepository->getDossiersReadyForLiquidation();
    }

    /**
     * Create workflow steps for dossier
     */
    private function createWorkflowSteps(DossierPension $dossier): void
    {
        $steps = WorkflowStep::getDefaultWorkflowSteps($dossier->type_pension);
        
        $previousStep = null;
        foreach ($steps as $stepData) {
            $step = $dossier->workflowSteps()->create([
                'step_name' => $stepData['name'],
                'step_order' => $stepData['order'],
                'statut' => $stepData['order'] === 1 ? WorkflowStep::STATUS_EN_COURS : WorkflowStep::STATUS_EN_ATTENTE,
                'date_debut' => $stepData['order'] === 1 ? now() : null,
            ]);

            if ($previousStep) {
                $previousStep->update(['next_step_id' => $step->id]);
            }

            $previousStep = $step;
        }
    }

    /**
     * Advance workflow to next step
     */
    private function advanceWorkflow(DossierPension $dossier, string $currentStepName): void
    {
        $currentStep = $dossier->workflowSteps()
                              ->where('step_name', $currentStepName)
                              ->first();

        if ($currentStep) {
            $currentStep->complete(Auth::id());

            // Start next step if exists
            if ($currentStep->nextStep) {
                $currentStep->nextStep->start();
            }
        }
    }

    /**
     * Export dossiers data
     */
    public function exportDossiers(array $filters = []): array
    {
        $dossiers = $this->dossierRepository->getDossiersForExport($filters);
        
        return $dossiers->map(function ($dossier) {
            return [
                'Numéro Dossier' => $dossier->numero_dossier,
                'Adhérent' => $dossier->adherent->full_name,
                'Numéro Adhérent' => $dossier->adherent->numero_adherent,
                'Type Pension' => $dossier->type_label,
                'Date Demande' => $dossier->date_demande->format('d/m/Y'),
                'Date Départ Retraite' => $dossier->date_depart_retraite->format('d/m/Y'),
                'Salaire Référence' => $dossier->salaire_reference,
                'Durée Cotisation (mois)' => $dossier->duree_cotisation_mois,
                'Taux Liquidation' => ($dossier->taux_liquidation * 100) . '%',
                'Montant Calculé' => $dossier->montant_pension_calcule,
                'Montant Liquidé' => $dossier->montant_pension_liquide,
                'Statut' => $dossier->status_label,
                'Date Liquidation' => $dossier->date_liquidation?->format('d/m/Y'),
            ];
        })->toArray();
    }

    /**
     * Simulate pension calculation
     */
    public function simulatePension(Adherent $adherent, array $parameters): array
    {
        $cotisations = $adherent->cotisations()->paid()->get();
        
        $referenceSalary = $parameters['salaire_reference'] ?? 
                          $cotisations->sortByDesc('periode_cotisation')
                                     ->take(60)
                                     ->avg('montant_base');

        $contributionMonths = $parameters['duree_cotisation_mois'] ?? $cotisations->count();
        $yearsOfContribution = $contributionMonths / 12;
        
        // Calculate pension rate (2% per year, max 75%)
        $pensionRate = min($yearsOfContribution * 0.02, 0.75);
        $grossPension = $referenceSalary * $pensionRate;
        
        // Calculate retenues (simplified)
        $fiscalRetenues = $grossPension > 100000 ? $grossPension * 0.10 : 0;
        $socialRetenues = $grossPension * 0.05;
        $netPension = $grossPension - $fiscalRetenues - $socialRetenues;

        return [
            'salaire_reference' => $referenceSalary,
            'duree_cotisation_mois' => $contributionMonths,
            'duree_cotisation_annees' => round($yearsOfContribution, 2),
            'taux_liquidation' => $pensionRate,
            'montant_brut' => $grossPension,
            'retenues_fiscales' => $fiscalRetenues,
            'retenues_sociales' => $socialRetenues,
            'montant_net' => $netPension,
        ];
    }
}
