<?php

namespace Modules\Cotisations;

use App\Providers\BaseModuleServiceProvider;

class CotisationsServiceProvider extends BaseModuleServiceProvider
{
    /**
     * Get module name
     */
    protected function getModuleName(): string
    {
        return "Cotisations";
    }

    /**
     * Register module services
     */
    protected function registerServices(): void
    {
        // Register repositories
        $this->app->bind(\Modules\Cotisations\Repositories\CotisationRepository::class, function ($app) {
            return new \Modules\Cotisations\Repositories\CotisationRepository(new \Modules\Cotisations\Models\Cotisation());
        });

        // Register services
        $this->app->bind(\Modules\Cotisations\Services\CotisationService::class);
    }
}