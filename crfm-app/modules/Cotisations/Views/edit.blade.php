@extends('layouts.template')

@section('title', isset($cotisation) ? 'Modifier Cotisation - CRFM' : 'Nouvelle Cotisation - CRFM')

@section('page-header')
@section('page-title', isset($cotisation) ? 'Modifier la cotisation' : 'Nouvelle Cotisation')
@section('page-description', isset($cotisation) ? $cotisation->numero_cotisation . ' - ' . $cotisation->periode_cotisation : 'Créer une nouvelle cotisation pour un adhérent')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('cotisations.index') }}">Cotisations</a>
        </li>
        @if(isset($cotisation))
        <li class="breadcrumb-item">
            <a href="{{ route('cotisations.show', $cotisation) }}">{{ $cotisation->numero_cotisation }}</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Modifier</a></li>
        @else
        <li class="breadcrumb-item"><a href="#!">Nouvelle</a></li>
        @endif
    </ul>
@endsection
@endsection

@section('content')
<form method="POST" action="{{ isset($cotisation) ? route('cotisations.update', $cotisation) : route('cotisations.store') }}" class="needs-validation" novalidate>
    @if(isset($cotisation))
        @method('PUT')
    @endif
    @csrf
    
    <div class="row">
        <!-- Informations principales -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-credit-card text-c-blue me-2"></i>Informations de la cotisation</h5>
                </div>
                <div class="card-block">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Adhérent</label>
                                <select name="adherent_id" class="form-control @error('adherent_id') is-invalid @enderror" required {{ isset($cotisation) ? 'disabled' : '' }}>
                                    <option value="">Sélectionner un adhérent</option>
                                    @foreach($adherents as $adherent)
                                        <option value="{{ $adherent->id }}" {{ old('adherent_id', $cotisation->adherent_id ?? '') == $adherent->id ? 'selected' : '' }}>
                                            {{ $adherent->full_name }} - {{ $adherent->numero_adherent }}
                                        </option>
                                    @endforeach
                                </select>
                                @if(isset($cotisation))
                                    <input type="hidden" name="adherent_id" value="{{ $cotisation->adherent_id }}">
                                    <small class="text-muted">L'adhérent ne peut pas être modifié</small>
                                @endif
                                @error('adherent_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Numéro de cotisation</label>
                                <input type="text" class="form-control" value="{{ $cotisation->numero_cotisation ?? 'Généré automatiquement' }}" readonly>
                                <small class="text-muted">{{ isset($cotisation) ? 'Le numéro ne peut pas être modifié' : 'Le numéro sera généré automatiquement' }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="required">Année</label>
                                <select name="annee_cotisation" class="form-control @error('annee_cotisation') is-invalid @enderror" required>
                                    @for($year = date('Y'); $year >= date('Y') - 2; $year--)
                                        <option value="{{ $year }}" {{ old('annee_cotisation', $cotisation->annee_cotisation ?? date('Y')) == $year ? 'selected' : '' }}>
                                            {{ $year }}
                                        </option>
                                    @endfor
                                </select>
                                @error('annee_cotisation')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="required">Mois</label>
                                <select name="mois_cotisation" class="form-control @error('mois_cotisation') is-invalid @enderror" required>
                                    <option value="">Sélectionner</option>
                                    @php
                                        $months = [
                                            1 => 'Janvier', 2 => 'Février', 3 => 'Mars', 4 => 'Avril',
                                            5 => 'Mai', 6 => 'Juin', 7 => 'Juillet', 8 => 'Août',
                                            9 => 'Septembre', 10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
                                        ];
                                    @endphp
                                    @foreach($months as $num => $name)
                                        <option value="{{ $num }}" {{ old('mois_cotisation', date('n')) == $num ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('mois_cotisation')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Type de cotisation</label>
                                <select name="type_cotisation" class="form-control @error('type_cotisation') is-invalid @enderror">
                                    <option value="normale" {{ old('type_cotisation', 'normale') === 'normale' ? 'selected' : '' }}>Normale</option>
                                    <option value="rattrapage" {{ old('type_cotisation') === 'rattrapage' ? 'selected' : '' }}>Rattrapage</option>
                                    <option value="complementaire" {{ old('type_cotisation') === 'complementaire' ? 'selected' : '' }}>Complémentaire</option>
                                    <option value="exceptionnelle" {{ old('type_cotisation') === 'exceptionnelle' ? 'selected' : '' }}>Exceptionnelle</option>
                                </select>
                                @error('type_cotisation')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Salaire de référence (FCFA)</label>
                                <input type="number" name="salaire_reference" class="form-control @error('salaire_reference') is-invalid @enderror" 
                                       value="{{ old('salaire_reference') }}" min="0" step="1000" required>
                                @error('salaire_reference')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Taux de cotisation (%)</label>
                                <input type="number" name="taux_cotisation" class="form-control @error('taux_cotisation') is-invalid @enderror" 
                                       value="{{ old('taux_cotisation', 18) }}" min="0" max="100" step="0.1">
                                @error('taux_cotisation')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Montant cotisation (FCFA)</label>
                                <input type="number" name="montant_cotisation" class="form-control @error('montant_cotisation') is-invalid @enderror" 
                                       value="{{ old('montant_cotisation') }}" min="0" step="100" required>
                                @error('montant_cotisation')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Date d'échéance</label>
                                <input type="date" name="date_echeance" class="form-control @error('date_echeance') is-invalid @enderror" 
                                       value="{{ old('date_echeance', date('Y-m-t')) }}">
                                @error('date_echeance')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Observations</label>
                        <textarea name="observations" class="form-control @error('observations') is-invalid @enderror" 
                                  rows="3">{{ old('observations') }}</textarea>
                        @error('observations')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Informations complémentaires -->
        <div class="col-md-4">
            <!-- Calculateur automatique -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-calculator text-c-green me-2"></i>Calculateur automatique</h5>
                </div>
                <div class="card-block">
                    <div class="alert alert-info">
                        <i class="feather icon-info me-2"></i>
                        <small>Le montant sera calculé automatiquement selon le salaire et le taux</small>
                    </div>
                    
                    <div class="form-group">
                        <label>Salaire de base</label>
                        <input type="number" id="salaire_base" class="form-control" placeholder="Saisir le salaire">
                    </div>
                    
                    <div class="form-group">
                        <label>Taux (%)</label>
                        <input type="number" id="taux" class="form-control" value="18" step="0.1">
                    </div>
                    
                    <button type="button" class="btn btn-success btn-block" onclick="calculateCotisation()">
                        <i class="feather icon-calculator me-2"></i>Calculer
                    </button>
                    
                    <div id="calculResult" class="mt-3" style="display: none;">
                        <div class="alert alert-success">
                            <strong>Montant calculé:</strong>
                            <h4 id="montantCalcule" class="text-success"></h4>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Historique des cotisations -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-clock text-c-yellow me-2"></i>Historique récent</h5>
                </div>
                <div class="card-block">
                    <div id="adherentHistory">
                        <p class="text-muted text-center">Sélectionnez un adhérent pour voir l'historique</p>
                    </div>
                </div>
            </div>
            
            <!-- Aide -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-help-circle text-c-purple me-2"></i>Aide</h5>
                </div>
                <div class="card-block">
                    <h6>Types de cotisation:</h6>
                    <ul class="list-unstyled">
                        <li><strong>Normale:</strong> Cotisation mensuelle standard</li>
                        <li><strong>Rattrapage:</strong> Cotisation en retard</li>
                        <li><strong>Complémentaire:</strong> Cotisation supplémentaire</li>
                        <li><strong>Exceptionnelle:</strong> Cotisation spéciale</li>
                    </ul>
                    
                    <hr>
                    
                    <h6>Calcul automatique:</h6>
                    <p class="text-muted small">
                        Montant = Salaire × Taux ÷ 100
                        <br>Taux standard: 18%
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Boutons d'action -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-block text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="feather icon-save me-2"></i>Créer la cotisation
                    </button>
                    <a href="{{ route('cotisations.index') }}" class="btn btn-secondary btn-lg ms-2">
                        <i class="feather icon-x me-2"></i>Annuler
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('styles')
<style>
.required::after {
    content: " *";
    color: red;
}
</style>
@endpush

@push('scripts')
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Calculateur de cotisation
function calculateCotisation() {
    const salaire = parseFloat(document.getElementById('salaire_base').value);
    const taux = parseFloat(document.getElementById('taux').value);
    
    if (!salaire || !taux) {
        alert('Veuillez saisir le salaire et le taux');
        return;
    }
    
    const montant = Math.round(salaire * taux / 100);
    
    document.getElementById('montantCalcule').textContent = montant.toLocaleString() + ' FCFA';
    document.getElementById('calculResult').style.display = 'block';
    
    // Remplir automatiquement les champs
    document.querySelector('input[name="salaire_reference"]').value = salaire;
    document.querySelector('input[name="taux_cotisation"]').value = taux;
    document.querySelector('input[name="montant_cotisation"]').value = montant;
}

// Charger l'historique de l'adhérent
document.querySelector('select[name="adherent_id"]').addEventListener('change', function() {
    const adherentId = this.value;
    if (!adherentId) {
        document.getElementById('adherentHistory').innerHTML = '<p class="text-muted text-center">Sélectionnez un adhérent pour voir l\'historique</p>';
        return;
    }
    
    fetch(`/api/adherents/${adherentId}/cotisations-history`)
        .then(response => response.json())
        .then(data => {
            let html = '';
            if (data.length > 0) {
                html = '<h6>Dernières cotisations:</h6>';
                data.forEach(cotisation => {
                    html += `
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div>
                                <small><strong>${cotisation.periode}</strong></small><br>
                                <small class="text-muted">${cotisation.montant} FCFA</small>
                            </div>
                            <span class="badge bg-${cotisation.status_color}">${cotisation.status}</span>
                        </div>
                    `;
                });
            } else {
                html = '<p class="text-muted text-center">Aucune cotisation trouvée</p>';
            }
            document.getElementById('adherentHistory').innerHTML = html;
        })
        .catch(error => {
            console.error('Erreur:', error);
            document.getElementById('adherentHistory').innerHTML = '<p class="text-danger text-center">Erreur de chargement</p>';
        });
});

// Auto-calcul quand on change le salaire ou le taux
document.querySelector('input[name="salaire_reference"]').addEventListener('input', function() {
    const salaire = parseFloat(this.value);
    const taux = parseFloat(document.querySelector('input[name="taux_cotisation"]').value);
    
    if (salaire && taux) {
        const montant = Math.round(salaire * taux / 100);
        document.querySelector('input[name="montant_cotisation"]').value = montant;
    }
});

document.querySelector('input[name="taux_cotisation"]').addEventListener('input', function() {
    const taux = parseFloat(this.value);
    const salaire = parseFloat(document.querySelector('input[name="salaire_reference"]').value);
    
    if (salaire && taux) {
        const montant = Math.round(salaire * taux / 100);
        document.querySelector('input[name="montant_cotisation"]').value = montant;
    }
});
</script>
@endpush
