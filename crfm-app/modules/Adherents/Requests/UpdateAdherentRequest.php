<?php

namespace Modules\Adherents\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateAdherentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->user()->can('manage-adherents');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $adherentId = $this->route('adherent')->id;

        return [
            // Informations personnelles
            'nom' => 'required|string|max:255',
            'prenoms' => 'required|string|max:255',
            'date_naissance' => 'required|date|before:today',
            'lieu_naissance' => 'required|string|max:255',
            'sexe' => 'required|in:M,F',
            'situation_matrimoniale' => 'required|in:celibataire,marie,divorce,veuf',
            'nationalite' => 'nullable|string|max:255',
            
            // Informations professionnelles
            'profession' => 'required|string|max:255',
            'employeur' => 'required|string|max:255',
            'date_embauche' => 'nullable|date|before_or_equal:today',
            'salaire_base' => 'nullable|numeric|min:0',
            
            // Contacts
            'telephone' => 'required|string|max:20',
            'email' => 'nullable|email|max:255',
            'adresse_domicile' => 'required|string',
            'adresse_professionnelle' => 'nullable|string',
            'contact_urgence_nom' => 'nullable|string|max:255',
            'contact_urgence_telephone' => 'nullable|string|max:20',
            
            // Documents d'identité
            'numero_cni' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('adherents', 'numero_cni')->ignore($adherentId)
            ],
            'date_delivrance_cni' => 'nullable|date|before_or_equal:today',
            'lieu_delivrance_cni' => 'nullable|string|max:255',
            'numero_passeport' => 'nullable|string|max:50',
            
            // Informations adhésion
            'date_adhesion' => 'required|date|before_or_equal:today',
            'statut' => 'required|in:actif,suspendu,radie,retraite',
            'observations' => 'nullable|string',
            
            // Bénéficiaires
            'beneficiaires' => 'nullable|array',
            'beneficiaires.*.nom' => 'required_with:beneficiaires|string|max:255',
            'beneficiaires.*.prenoms' => 'required_with:beneficiaires|string|max:255',
            'beneficiaires.*.date_naissance' => 'required_with:beneficiaires|date|before:today',
            'beneficiaires.*.lieu_naissance' => 'required_with:beneficiaires|string|max:255',
            'beneficiaires.*.sexe' => 'required_with:beneficiaires|in:M,F',
            'beneficiaires.*.lien_parente' => 'required_with:beneficiaires|in:conjoint,enfant,parent,frere_soeur,autre',
            'beneficiaires.*.telephone' => 'nullable|string|max:20',
            'beneficiaires.*.adresse' => 'nullable|string',
            'beneficiaires.*.pourcentage_benefice' => 'required_with:beneficiaires|numeric|min:0|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'nom.required' => 'Le nom est obligatoire.',
            'prenoms.required' => 'Les prénoms sont obligatoires.',
            'date_naissance.required' => 'La date de naissance est obligatoire.',
            'date_naissance.before' => 'La date de naissance doit être antérieure à aujourd\'hui.',
            'lieu_naissance.required' => 'Le lieu de naissance est obligatoire.',
            'sexe.required' => 'Le sexe est obligatoire.',
            'sexe.in' => 'Le sexe doit être M ou F.',
            'situation_matrimoniale.required' => 'La situation matrimoniale est obligatoire.',
            'profession.required' => 'La profession est obligatoire.',
            'employeur.required' => 'L\'employeur est obligatoire.',
            'telephone.required' => 'Le numéro de téléphone est obligatoire.',
            'email.email' => 'L\'adresse email doit être valide.',
            'adresse_domicile.required' => 'L\'adresse de domicile est obligatoire.',
            'numero_cni.unique' => 'Ce numéro de CNI existe déjà.',
            'date_adhesion.required' => 'La date d\'adhésion est obligatoire.',
            'date_adhesion.before_or_equal' => 'La date d\'adhésion ne peut pas être dans le futur.',
            'statut.required' => 'Le statut est obligatoire.',
            'salaire_base.numeric' => 'Le salaire de base doit être un nombre.',
            'salaire_base.min' => 'Le salaire de base ne peut pas être négatif.',
            
            // Messages pour les bénéficiaires
            'beneficiaires.*.nom.required_with' => 'Le nom du bénéficiaire est obligatoire.',
            'beneficiaires.*.prenoms.required_with' => 'Les prénoms du bénéficiaire sont obligatoires.',
            'beneficiaires.*.date_naissance.required_with' => 'La date de naissance du bénéficiaire est obligatoire.',
            'beneficiaires.*.lieu_naissance.required_with' => 'Le lieu de naissance du bénéficiaire est obligatoire.',
            'beneficiaires.*.sexe.required_with' => 'Le sexe du bénéficiaire est obligatoire.',
            'beneficiaires.*.lien_parente.required_with' => 'Le lien de parenté est obligatoire.',
            'beneficiaires.*.pourcentage_benefice.required_with' => 'Le pourcentage de bénéfice est obligatoire.',
            'beneficiaires.*.pourcentage_benefice.max' => 'Le pourcentage de bénéfice ne peut pas dépasser 100%.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Vérifier que la somme des pourcentages des bénéficiaires ne dépasse pas 100%
            if ($this->has('beneficiaires')) {
                $totalPourcentage = collect($this->beneficiaires)
                    ->sum('pourcentage_benefice');
                
                if ($totalPourcentage > 100) {
                    $validator->errors()->add('beneficiaires', 'La somme des pourcentages des bénéficiaires ne peut pas dépasser 100%.');
                }
            }
        });
    }
}
