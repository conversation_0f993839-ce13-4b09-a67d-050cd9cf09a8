<!DOCTYPE html>
<html>
<head>
  <title>Transitionize Example Page</title>
  <style type="text/css">
    .wrap {
      height: 75px;
      margin: 100px auto 0;
      position: relative;
      width: 300px;
    }

    .elem {
      background-color: #febf04;
      border-radius: 100%;
      bottom: 0;
      display: block;
      height: 75px;
      left: 0;
      position: absolute;
      width: 75px;
    }
  </style>
  <script src="bundle.js"></script>
</head>
<body>
  <div class="wrap">
    <span class="elem js-elem"></span>
  </div>
</body>
</html>