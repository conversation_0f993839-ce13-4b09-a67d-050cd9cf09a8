/*  TABLE OF CONTENTS
    ---------------------------
    *. @Import & Reset
    *. Loading overlay
    1. Generic styles
    2. Slider wrapper

        2.1 Slide colors

    3. Top info bar
    4. Slide content
    5. Newsletter
    6. Scroll Bar
    7. Media Queries
*/

/* ------------------------------------- */
/* *. @Import & Reset .................. */
/* ------------------------------------- */

/* http://bourbon.io/ Sass Mixin Library */
@import "bourbon";

// colors, fonts, sizes etc...
@import "vars";

// custom mixins
@import "mixins";

/* custom CSS files */
@import "font-awesome.min.css";
@import "ionicons.min.css";
@import "bootstrap.min.css";
@import "animate.css";
@import "photoswipe.css";
@import "default-skin/default-skin.css";
@import "jquery.mCustomScrollbar.css";

/* Google Fonts */
@import url("http://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700");

@import url("https://fonts.googleapis.com/css?family=Raleway:300,400,500,700");

@import url("https://fonts.googleapis.com/css?family=Montserrat:400,700");

/*
* http://meyerweb.com/eric/tools/css/reset/ 
* v2.0 | 20110126
* License: none (public domain)
*/

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section, main {
    display: block;
}

body {
    line-height: 1;
}

ol, ul {
    list-style: none;
}

blockquote, q {
    quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
    content: '';
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

/* ------------------------------------- */
/* Loading overlay ..................... */
/* ------------------------------------- */

#loading {
    width: 100vw;
    height: 100vh;
    background: rgba($outer-space, 0.7);
    position: fixed;
    z-index: 1000000;
    @include bezier-transition(all, $average-transition, ease-in-out);

    &.dark-back {
        background: rgba($outer-space, 1);
    }

    .loading-bar {
        position: relative;
        overflow: hidden;
        top: 0;
        height: 0.3rem;
        width: 100vw;
        background: rgba($outer-space, 0.3);

        &:after {
            content: '';
            position: fixed;
            top: 0;
            z-index: 10;
            background: $white;
            height: 0.3rem;
            width: 30vw;
            @include border-radius(10rem);
            @include animation(baranim 4s infinite linear);
        }
    }

    span {
        position: fixed;
        width: 100%;
        top: 49vh;
            z-index: 10;
        font-size: 1rem;
        letter-spacing: 0.5rem;
        text-align: center;
        text-transform: uppercase;
    }
}

@include keyframes(baranim) {
    0% {
        @include transform(translateX(-30vw));
    }

    100% {
        @include transform(translateX(130vw));
    }
}

/* ------------------------------------- */
/* 1. Generic styles ................... */
/* ------------------------------------- */

html {
    font-size: 62.5%;
}

body {
    background: $black-squeeze;
    font-family: $montserrat-font;
    color: $white;
    @include font-smoothing(on);
    font-weight: normal;
    font-style: normal;
    font-size: 1.4rem;
    line-height: 1.8;
    font-weight: $normal;
    letter-spacing: 0;
    position: absolute;
    height: 100%;
}

.scroll-touch {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}

body, html {
    margin: 0;
    padding: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    width: 100%;
}

body, input, select, textarea {
    @include bezier-transition(all, $fast-transition, ease-in-out);
}

a {
    @include bezier-transition(all, $fast-transition, ease-in-out);
    cursor: pointer;
    text-decoration: none;
    color: $white;

    &:hover {
        color: darken($torea-bay, 5%);
        text-decoration: none !important;
        outline: none !important;
    }

    &:active, &:focus {
        outline: none !important;
        text-decoration: none !important;
        color: $white;
    }
}

button {
    @include bezier-transition(all, $fast-transition, ease-in-out);
    cursor: pointer;

    &:hover, &:active, &:focus {
        outline: none !important;
        text-decoration: none !important;
        color: $shark;
    }
}

strong, b {
    font-weight: $bold;
}

em, i {
    font-style: italic;
}

i {

        @include bezier-transition(all, $fast-transition, ease-in-out);
}

p {
    margin: 0;
    font-size: 1.4rem;
    line-height: 1.8;
    color: $dove-gray;
    font-weight: $normal;
}

h1, h2, h3, h4, h5, h6 {
    color: $outer-space;
    font-family: $montserrat-font;
    font-weight: $normal;
    line-height: 1;
    margin: 0 0 1.5rem 0;

    a {
        color: inherit;
        text-decoration: none;
    }

    small {
        color: inherit;
    }
}

h1 {
    font-size: 7rem;
}

h2 {
    font-size: 5rem;
}

h3 {
    font-size: 2.3rem;
}

h4 {
    font-size: 1.8rem;
}

h5 {
    font-size: 1.6rem;
}

h6 {
    font-size: 1.2rem;
}

sub {
    font-size: 0.8em;
    position: relative;
    top: 0.5em;
}

sup {
    font-size: 0.8em;
    position: relative;
    top: -0.5em;
}

.clear {
    clear: both;
}

.display-none {
    display: none !important;
}

.align-left {
    text-align: left;
}

.align-center {
    text-align: center;
}

.align-right {
    text-align: right;
}

.no-margin-bottom {
    margin-bottom: 0;
}

.opacity-0 {
    opacity: 0 !important;
    visibility: hidden !important;
}

.opacity-03 {
    opacity: 0.3 !important;
}

.opacity-1 {
    opacity: 1 !important;
    visibility: visible !important;
}

.index-999 {
    z-index: -999 !important;
}

.copyright {
    position: absolute;
    right: 1.5rem;
    bottom: 1rem;
    font-size: 1rem;
    z-index: 200;
    font-family: $raleway-font;

    a {
        color: $torea-bay;
    }
}

#particles-js {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 1;
}

/* ------------------------------------- */
/* 2. Slider wrapper ................... */
/* ------------------------------------- */

.sl-slider-wrapper {
	overflow: hidden;
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;

	.sl-slider {
		position: absolute;
		top: 0;
		left: 0;

		/* Slide wrapper and slides */
		.sl-slide,
		.sl-slides-wrapper,
		.sl-slide-inner {
			position: absolute;
			width: 100%;
			height: 100%;
			top: 0;
			left: 0;
		}

		.sl-slide-inner {
            @include align-items(center);
            @include display(flex);
            @include justify-content(center);
		}

		.sl-slide-inner, .sl-content-slice {
			background-size: cover;
		}

		.sl-slide {
			z-index: 1;
		}
	}
}

/* ------------------------------------- */
/* 2.1 Slide colors .................... */
/* ------------------------------------- */

.bg-1 {
    .sl-slide-inner , .sl-content-slice {
        background: transparent;
    }
}

/* ------------------------------------- */
/* 3. Top info bar ..................... */
/* ------------------------------------- */

.info-bar {
	background: rgba($white, 0.6);
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	padding: 2rem;
	z-index: 1000;

	#countdown {
		color: $torea-bay;
	}

	p {
		color: lighten($trout, 20%);
	}

	.info-bar-right {
		text-align: right;

        p.social-text {

    		a {
    			margin: 0 0.4rem;
                color: lighten($trout, 20%);

                &:hover {
                    color: $torea-bay;
                }

    			&:last-child {
    				margin-right: 0;
    			}
    		}
        }

        p.social-icon {

            a {
                color: lighten($trout, 20%);
                width: 3rem;
                letter-spacing: 0;
                background: transparent;
                font-size: 1.5rem;
                display: inline-block;
                text-align: center;
                border: none;
                @include bezier-transition(all, $fast-transition, ease-in-out);

                &:hover {
                    color: $torea-bay;

                    i {
                        color: $torea-bay;
                    }
                }
            }

        }
	}
}

/* ------------------------------------- */
/* 4. Slide content .................... */
/* ------------------------------------- */

.content-slide {
	position: relative;
    z-index: 10;
    left: 0;
    padding: 0;
    margin-left: auto;
    margin-right: auto;
    width: 100%;
    max-height: 100vh;
    padding: 0;
    @include bezier-transition(all, $average-transition, ease-in-out);
    text-align: center;
    @include font-smoothing;
    opacity: 1;
    visibility: visible;

    .container {
        padding: 13rem 10rem 20rem;
    }

    .brand-logo {
        max-width: 14rem;
	    margin-bottom: 2rem;
    }

    h1 {
    	text-transform: uppercase;
    }

    h2 {
        margin-bottom: 3rem;
    }

    p {
    	font-family: $raleway-font;
    	font-size: 1.7rem;
    	color: $black;

    	&.resume {
    		padding: 0 10rem;
    	}
    }

    .action-btn {
	    font-family: $montserrat-font;
	    background: $torea-bay;
	    font-weight: $normal;
	    padding: 1rem 2.5rem;
	    color: $white;
        position: relative;
	    @include border-radius(10rem);
	    border: 1px solid $torea-bay;
	    display: inline-block;
	    margin-top: 2rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        overflow: hidden;

        &:after {
            font-family: 'FontAwesome';
            content: '\f0a2';
            position: absolute;
            opacity: 0;
            top: 4px;
            right: -2rem;
            font-size: 2rem;
            @include bezier-transition(all, $fast-transition, ease-in-out);
            @include animation(ring 1.5s infinite linear);
        }

        @include keyframes(ring) {
            0% {
                @include transform(rotate(0deg));
            }
            6.25% {
                @include transform(rotate(-2deg));
            }
            12.5% {
                @include transform(rotate(5deg));
            }
            18.75% {
                @include transform(rotate(-10deg));
            }
            25% {
                @include transform(rotate(15deg));
            }
            31.25% {
                @include transform(rotate(-20deg));
            }
            37.5% {
                @include transform(rotate(25deg));
            }
            43.75% {
                @include transform(rotate(-30deg));
            }
            50% {
                @include transform(rotate(35deg));
            }
            56.25% {
                @include transform(rotate(-30deg));
            }
            62.5% {
                @include transform(rotate(25deg));
            }
            68.75% {
                @include transform(rotate(-20deg));
            }
            75% {
                @include transform(rotate(15deg));
            }
            81.25% {
                @include transform(rotate(-10deg));
            }
            87.5% {
                @include transform(rotate(5deg));
            }
            93.75% {
                @include transform(rotate(-2deg));
            }
            100% {
                @include transform(rotate(0deg));
            }
        }

	    &:hover {
	        background: lighten($torea-bay, 10%);
	        color: $white;
	        border-color: $torea-bay;
            padding-right: 4.5rem;

            &:after {
              opacity: 1;
              right: 1.5rem;
            }
	    }
	}
}

/* ------------------------------------- */
/* 5. Newsletter ....................... */
/* ------------------------------------- */

.dialog,
.dialog__overlay {
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.dialog {
    position: fixed;
    z-index: 999999;
    @include display(flex);
    @include justify-content(center);
    @include align-items(center);
    pointer-events: none;
}

.dialog__overlay {
    position: absolute;
    z-index: 1;
    background: rgba($outer-space, 0.9);
    opacity: 0;
    transition: opacity $fast-transition;
}

.dialog--open .dialog__overlay {
    opacity: 1;
    pointer-events: auto;
}

.dialog__content {
    width: 50%;
    max-width: 50rem;
    min-width: 290px;
    padding: 0;
    text-align: center;
    position: relative;
    z-index: 5;
    opacity: 0;
    overflow: hidden;
    border: 1px solid $outer-space;
    box-shadow: 0 1rem 2.5rem rgba($black, 0.4);
    background: $white;
    @include border-radius(5px);

    .header-picture {
        width: 100%;
        height: 18rem;
        background: url('../img/background-newsletter.jpg') center;
        background-size: cover;
    }

    .dialog-inner {
        padding: 5rem 5rem 0 5rem;
        overflow: hidden;
        background: url('../img/background-newsletter-inner.jpg') center;
        background-size: cover;

        &::before {
            content: " ";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }

        h4 {
            color: $outer-space;
            font-size: 4rem;
            line-height: 1.2;
            font-weight: $bold;
        }

        p {
            color: $dove-gray;
            font-size: 1.4rem;

            &.spam-news {
                margin-top: 6rem;
                margin-bottom: 2rem;
                color: darken($seashell, 20%);
                font-size: 1rem;
            }
        }
    }
}

.dialog--open .dialog__content {
    pointer-events: auto;
}

.dialog {
    .close-newsletter {
        position: absolute;
        top: 0;
        right: 0;
        border: none;
        background: rgba($white, 0.5);
        @include border-radius(0 0 0 5px);
        width: 50px;
        height: 50px;
        line-height: 0;
        color: lighten($outer-space, 30%);
        font-size: 2rem;
        opacity: 1;

        i {
        display: inline-block;
        @include bezier-transition(all, $fast-transition, ease-in-out);
        }

        &:hover {
            i {
                color: $outer-space;
            }
        }
    }
}

#subscribe {
    p {
        font-weight: $normal;
    }

    #notifyMe {
        max-width: 450px;
        margin: auto;
        margin-top: 2em;

        .form-group {
            margin-bottom: 1em;

            .fa {
                color: $raven;
                position: absolute;
                text-align: center;
                top: 15px;
                left: 15px;
            }

            .form-control {
                text-align: center;
                background: darken($athens-gray, 2%);
                border: 1px solid $seashell;
                @include border-radius(3px);
                box-shadow: none;
                height: 50px;
                font-weight: $semi-bold;
                outline: medium none;
                padding: 0 1em;
                width: 100%;
                margin: auto;
                color: $black;
                @include bezier-transition(all, $fast-transition, ease-in-out);

                &:hover, &:focus {
                    box-shadow: none;
                    background: darken($athens-gray, 5%);
                }

                @include placeholder {
                    color: rgba($black, 0.4) !important;
                }
            }

            button.submit {
                padding: 1.3rem 2.5rem;
                font-size: 1.4rem;
                display: block;
                margin: 2rem auto 0;
                background: $torea-bay;
                color: $white;
                @include border-radius(10rem);
                font-weight: $semi-bold;
                width: auto;

                &:hover {
                    background: lighten($torea-bay, 10%);
                    color: $white;
                }
            }
        }
    }

    .block-message {
        min-height: 50px;
        position: absolute;
        bottom: -100px;
        width: 100%;
        left: 0;
        padding: 15px;
        background: transparent;
        @include bezier-transition(all, $fast-transition, ease-in-out);

        &.show-block-error {
            bottom: 0;
            background: $torch-red;
        }

        &.show-block-valid {
            bottom: 0;
            background: $caribbean-green;
        }
    }

    p.notify-valid {
        color: $white;
        text-transform: none;
        font-size: 16px;
        letter-spacing: 0;
        font-weight: $semi-bold;
    }
}

.dialog__overlay {
    @include bezier-transition(all, $fast-transition, ease-in-out);
}

.dialog.dialog--open .dialog__content,
.dialog.dialog--close .dialog__content {
    @include animation-duration(0.4s);
    @include animation-fill-mode(forwards);
}

.dialog.dialog--open .dialog__content {
    @include animation-name(anim-open);
}

.dialog.dialog--close .dialog__content {
    @include animation-name(anim-close);
}

@include keyframes(anim-open) {
    0% {
        opacity: 0;
        @include transform-opal(-5rem, 0, 0);
    }

    100% {
        opacity: 1;
        @include transform-opal(0, 0, 0);
    }
}


@include keyframes(anim-close) {
    0% {
        opacity: 1;
        @include transform-opal(0, 0, 0);
    }

    100% {
        opacity: 0;
        @include transform-opal(5rem, 0, 0);
    }
}

/* ------------------------------------- */
/* 6. Scroll Bar ....................... */
/* ------------------------------------- */

.mCSB_scrollTools .mCSB_dragger:active .mCSB_dragger_bar,
.mCSB_scrollTools .mCSB_dragger.mCSB_dragger_onDrag .mCSB_dragger_bar {
    background: $white;
}

.mCSB_scrollTools .mCSB_dragger:hover .mCSB_dragger_bar {
    background: $white;
}

.mCSB_scrollTools {
    /* Scrollbar */
    position: absolute;
    width: 5px;
    left: auto;
    top: 10rem;
    right: 2rem;
    bottom: 0;
    z-index: 100000;
    height: calc(100% - 25rem);
    @include bezier-transition(all, $average-transition, ease-in-out);

    .mCSB_draggerRail {
        width: 2px;
        height: 100%;
        margin: 0;
        @include border-radius(5rem);
        background-color: rgba($outer-space, 0.7);
        filter: "alpha(opacity=40)";
        -ms-filter: "alpha(opacity=40)";
    }

    .mCSB_dragger .mCSB_dragger_bar {
        /* the dragger element */
        position: relative;
        width: 4px;
        right: 2px;
        height: 100%;
        margin: 0 auto;
        @include border-radius(5rem);
        text-align: center;
    }

    .mCSB_dragger .mCSB_dragger_bar {
        background-color: rgba($white, 0.5);
    }
}

/* ------------------------------------- */
/* 7. Media Queries .................... */
/* ------------------------------------- */

/* Large Devices, Wide Screens @media only screen and (max-width: 1600px) */
@include desktop {
}


/* Notebook devices @media only screen and (max-width: 1199px) */
@include notebook {
}


/* Medium Devices, Desktops @media only screen and (max-width: 993px) */
@include medium {
}


/* Small Devices, Tablets @media only screen and (max-width: 768px) */
@include tablet {

    h1 {
        font-size: 6rem;
    }

    .content-slide {

        .container {
            padding: 13rem 6rem 20rem;
        }

        .action-btn {

            &:after {
                display: none;
            }

            &:hover {
                padding-right: 2.5rem;
            }
        }
    }

    .dialog__content {
        width: auto;
    }
}

/* Special media queries used for the width just under the tablet one */
@include undertablet {
    .info-bar {
        padding: 1rem;
        text-align: center;

        .info-bar-right {
            margin-top: 1rem;
            margin-bottom: 0.8rem;
            text-align: center;
        }
    }
}

/* Extra Small Devices, Phones @media only screen and (max-width: 480px) */
@include phone {

    .content-slide {

        .container {
            padding-left: 2rem;
            padding-right: 2rem;
            padding-bottom: 15rem;

            .brand-logo {
                max-width: 10rem;
            }

            h1 {
                font-size: 3rem;
            }

            p {
                font-size: 1.4rem;
            }
        }
    }

    .mCSB_scrollTools {
        right: 0;
        height: calc(100% - 19rem);
    }

    .dialog__content {
        width: 95%;
        max-width: 95%;
        min-width: 75%;

        .header-picture {
            display: none;
        }
    }

    .dialog {
        .close-newsletter {
            top: 2px;
            right: 5px;
        }

        .dialog-inner {

            padding: 5rem 2rem 0 2rem;

            h4 {
                font-size: 25px;
                margin-bottom: 20px;
            }

            p.spam-news {
                margin-top: 2rem;
            }
        }
    }

    #subscribe {
        .block-message {
            padding: 5px 2px;
        }

        p.notify-valid {
            font-size: 12px;
        }
    }
}


/* Only for tablet in landscape mode @media only screen and (max-device-width: 1024px) and (orientation: landscape) */
@include landscape-tablet {
    .dialog__content {

        .header-picture {
            display: none;
        }
    }
}


/* Only for phone in landscape mode @media screen and (max-device-width: 667px) and (orientation: landscape) */
@include landscape-phone {
    
    .dialog__content {
        width: 100%;
        max-width: 100%;
        min-width: 75%;
    }

    .dialog {
        .close-newsletter {
            top: 2px;
            right: 5px;
        }

        .dialog-inner {
            padding: 40px 20px 50px;

            h4 {
                font-size: 25px;
                margin-bottom: 5px;
            }
        }
    }

    #subscribe {
        #notifyMe {
            margin-top: 10px;

            .form-group {
                .form-control {
                    width: 70%;
                    margin: 0;
                    float: left;
                }

                button.submit {
                    width: 30%;
                    margin: 0;
                    float: left;
                    @include border-radius(0);
                    height: 50px;
                }
            }
        }

        .block-message {
            padding: 5px 2px;
        }

        p.notify-valid {
            font-size: 12px;
        }
    }
}