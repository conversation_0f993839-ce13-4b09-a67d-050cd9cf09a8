// WebKit font-smoothing
// Values: none, antialiased (default), subpixel-antialiased
@mixin font-smoothing($value: on) {
    @if $value == on {
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    @else {
        -webkit-font-smoothing: subpixel-antialiased;
        -moz-osx-font-smoothing: auto;
    }
}

@mixin box-shadow($params) {
  -webkit-box-shadow: $params;
     -moz-box-shadow: $params;
          box-shadow: $params;
}

// Cross-browser mixins border-radius
@mixin border-radius($radius) {
    -webkit-border-radius: $radius;
       -moz-border-radius: $radius;
        -ms-border-radius: $radius;
            border-radius: $radius;
}

// Cross-browser mixins rotate
@mixin rotate($rotate) {
    -webkit-transform: rotate($rotate);
       -moz-transform: rotate($rotate);
        -ms-transform: rotate($rotate);
            transform: rotate($rotate);
}

// Devices Media Queries
$special-phone: 667px;
$special-tablet: 1024px;

// General Media Queries
$phone-width: 480px;
$tablet-width: 768px;
$medium-width: 992px;
$notebook-width: 1200px;

@mixin landscape-phone {
    @media screen and (max-device-width: $special-phone) and (orientation: landscape) {
        @content;
    }
}

@mixin landscape-tablet {
    @media only screen and (max-device-width: $special-tablet) and (orientation: landscape) {
        @content;
    }
}

@mixin phone {
    @media only screen and (max-width: $phone-width) {
        @content;
    }
}

@mixin tablet {
    @media only screen and (max-width: $tablet-width) {
        @content;
    }
}

@mixin medium {
    @media only screen and (max-width: $medium-width) {
        @content;
    }
}

@mixin notebook {
    @media only screen and (max-width: $notebook-width) {
        @content;
    }
}