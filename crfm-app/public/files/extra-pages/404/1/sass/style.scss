/*  TABLE OF CONTENTS
    ---------------------------
    *. @Import & Reset
    1. Generic styles
    2. Specific design
    3. Settings for variants
    4. Media Queries
*/

/* ------------------------------------- */
/* *. @Import & Reset .................. */
/* ------------------------------------- */

// http://bourbon.io/ Sass Mixin Library
@import "bourbon";

// colors, fonts, sizes etc...
@import "vars";

// custom mixins
@import "mixins";

/* custom CSS files */
@import "font-awesome.min.css";
@import "bootstrap.min.css";
@import "vegas.min.css";

/* Google Fonts */
@import url('https://fonts.googleapis.com/css?family=Montserrat|Open+Sans');

/*
* http://meyerweb.com/eric/tools/css/reset/ 
* v2.0 | 20110126
* License: none (public domain)
*/

html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
    margin: 0;
    padding: 0;
    border: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section, main {
    display: block;
}

body {
    line-height: 1;
}

ol, ul {
    list-style: none;
}

blockquote, q {
    quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
    content: '';
    content: none;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

/* ------------------------------------- */
/* 1. Generic styles ................... */
/* ------------------------------------- */

html {
    font-size: 62.5%;
}

body {
    background: $tuna;
    font-family: $open-font;
    color: $black;
    @include font-smoothing(on);
    font-weight: normal;
    font-style: normal;
    font-size: 1.4rem;
    line-height: 1.8;
    font-weight: $normal;
    letter-spacing: 0;
    height: 100%;

    &.flat {
        background: $true-violet;
    }

    &.bubble {

        background: url('../img/background-bubble.jpg') center;
        background-size: cover;

        &::after {
            content: '';
            position: absolute;
            z-index: 5;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba($tuna, 0.8);
        }
    }
}

body, html {
    margin: 0;
    padding: 0;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    width: 100%;
}

body, input, select, textarea {
    @include transition(all, $fast-transition, ease-in-out);
}

a {
    @include transition(all, $slow-transition, ease-in-out);
    cursor: pointer;
    text-decoration: none;
    color: $white;
    font-family: $montserrat-font;

    &:hover {
        color: darken($caribbean-green, 5%);
        text-decoration: none !important;
        outline: none !important;
    }

    &:active, &:focus {
        outline: none !important;
        text-decoration: none !important;
        color: $white;
    }
}

button {
    @include transition(all, $fast-transition, ease-in-out);
    cursor: pointer;

    &:hover, &:active, &:focus {
        outline: none !important;
        text-decoration: none !important;
        color: $shark;
    }
}

strong, b {
    font-weight: $bold;
}

em, i {
    font-style: italic;
}

p {
    font-family: $open-font;
    margin: 0;
    font-size: 1.5rem;
    line-height: 1.8;
    color: darken($athens-gray, 10%);
    font-weight: $normal;
    text-align: center;

    &.subtitle {
        margin-bottom: 3rem;
    }
}

h1, h2, h3, h4, h5, h6 {
    color: $white;
    font-family: $montserrat-font;
    font-weight: $normal;
    text-transform: uppercase;
    line-height: 1;
    margin: 0 0 1.5rem 0;
    text-align: center;

    a {
        color: inherit;
        text-decoration: none;
    }

    small {
        color: inherit;
    }
}

h1 {
    font-size: 3rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.8rem;
}

h4 {
    font-size: 1.3rem;
}

h5 {
    font-size: 1rem;
}

h6 {
    font-size: 1rem;
}

sub {
    font-size: 0.8em;
    position: relative;
    top: 0.5em;
}

sup {
    font-size: 0.8em;
    position: relative;
    top: -0.5em;
}

.clear {
    clear: both;
}

.display-none {
    display: none !important;
}

.align-left {
    text-align: left;
}

.align-center {
    text-align: center;
}

.align-right {
    text-align: right;
}

.index-999 {
    z-index: -999 !important;
}

.row-no-margin {
    margin: 0;
}

.no-padding {
    padding: 0;
}

/* ------------------------------------- */
/* 2. Specific design .................. */
/* ------------------------------------- */

.logo-link {
    position: absolute;
    z-index: 20;
    display: block;
    top: 3rem;
    left: 3rem;
    width: 5rem;

    .logo {
        width: 100%;
        height: auto;
    }
}

.content {
    height: 100vh;
    overflow: hidden;
    z-index: 10;
    position: relative;
    @include align-items(center);
    @include display(flex);
    @include justify-content(center);

    .content-box {
        width: auto;
        position: relative;

        .big-content {
            position: relative;
            width: 22rem;
            min-height: 22rem;
            margin: 0 auto 3rem;

            .list-square {
                float: left;
            }

            .list-line {
                float: left;
                margin-left: 2rem;
            }

            span.square {
                display: block;
                background: transparent;
                width: 5rem;
                height: 5rem;
                @include border-radius(0.5rem);
                border: 1rem solid $white;
                margin-bottom: 1.5rem;
            }

            span.line {
                display: block;
                background: $white;
                width: 15rem;
                height: 1rem;
                @include border-radius(0.5rem);
                margin-bottom: 2.3rem;

                &:nth-child(2) {
                    width: 10rem;
                }

                &:nth-child(4) {
                    width: 10rem;
                }

                &:nth-child(6) {
                    width: 10rem;
                }
            }

            .fa-search {
                position: absolute;
                top: 10rem;
                left: 15rem;
                font-size: 10rem;
                color: $caribbean-green;
                @include animation(corner 5s infinite);

                &.color {
                    color: $rose;
                }
            }
        }
    }
}

@include keyframes(corner) {
    0% {
        @include transform(translate(-2rem,0));
        @include animation-timing-function(0,.02,0,1.01);
    }

    20% {
        @include transform(translate(-15rem,2rem));
    }

    40% {
        @include transform(translate(-7rem,-7rem));
        animation-timing-function: cubic-bezier(0,.02,0,1.01);
    }

    60% {
        @include transform(translate(-15rem,-10rem));
    }

    80% {
        @include transform(translate(2rem,-12rem));
    }

    100% {
        @include transform(translate(-2rem,0));
    }
}

footer {
    color: $white;
    text-align: center;
    position: absolute;
    z-index: 20;
    padding: 1rem 0;
    bottom: 0;
    left: 0;
    width: 100%;

    ul {

        li {
            position: relative;
            display: inline-block;
            padding: 0;

            &::after {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 0.2rem;
                height: 100%;
                @include border-radius(1rem);
                background: darken($true-violet, 5%);
            }

            &:last-child {

                &::after {
                    display: none;
                }
            }

            a {
                text-transform: uppercase;
                display: block;
                width: 100%;
                height: 100%;
                padding: 0 2rem 0 1.4rem;

                &:hover {
                    color: darken($true-violet, 10%);

                    &::after {
                        opacity: 1;
                        @include transform(translateY(0) translateX(-60%));
                    }
                }

                &::after {
                    position: absolute;
                    top: 100%;
                    left: 50%;
                    width: 40%;
                    height: 0.4rem;
                    background: rgba(darken($true-violet,10%),0.4);
                    content: '';
                    opacity: 0;
                    @include border-radius(1rem);
                    @include transform(translateY(1rem) translateX(-60%));
                    @include transition(all, $slow-transition, ease-in-out);
                }
            }
        }
    }

    &.light {

        ul {

            li {

                &::after {
                    width: 0.1rem;
                    background: rgba($raven, 0.2);
                }

                a {
                    color: rgba($white, 0.7);

                    &:hover {
                        color: $white;
                    }

                    &::after {
                        background: rgba($white,0.3);
                    }
                }
            }
        }
    }
}

/* ------------------------------------- */
/* 3. Settings for variants ............ */
/* ------------------------------------- */

    /* ------------------------------------- */
    /* YouTube variant ..................... */
    /* ------------------------------------- */

    .mbYTP_wrapper {
        width: 100vw !important;
        min-width: 0 !important;
        left: 0 !important;

        &::after {
            content: '';
            position: absolute;
            z-index: 10;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba($kind-shark, 0.4);
        }
    }

    /* ------------------------------------- */
    /* FLAT variant ........................ */
    /* ------------------------------------- */

    #particles-js {
        position: fixed;
        top: 25vh;
        left: 25vw;
        width: 50vw;
        height: 50vh;
        z-index: -10;
    }

    /* ------------------------------------- */
    /* Image variant ....................... */
    /* ------------------------------------- */

    .image {
        background: url('../img/image.jpg') center;
        background-size: cover;
        width: 100vw;
        height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 5;
        @include animation(kenburns 30s infinite);

        &::after {
            content: '';
            position: absolute;
            z-index: -5;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba($kind-shark, 0.4);
        }
    }

    @include keyframes(kenburns) {
      0% {
        @include transform(scale(1));
      }
      50% {
        @include transform(scale(1.2));
      }
      100% {
        @include transform(scale(1));
      }
    }

    /* ------------------------------------- */
    /* Slideshow variant ................... */
    /* ------------------------------------- */

    .vegas-overlay {
        opacity: 1;
        background: rgba($kind-shark, 0.4);
    }

    .vegas-timer-progress {
        background: $lynch;
    }

    /* ------------------------------------- */
    /* Bubble variant ...................... */
    /* ------------------------------------- */

    #canvasbg, #canvas {
        position: fixed;
        z-index: 10;
        top: 0;
        left: 0;
        background: transparent;
    }

    /* ------------------------------------- */
    /* Mozaïc variant ...................... */
    /* ------------------------------------- */

    #dotty {
        position: fixed;
        top: 0;
        left: 0;
    }

/* ------------------------------------- */
/* 4. Media Queries .................... */
/* ------------------------------------- */

/* Notebook devices @media only screen and (max-width: 1200px) */
@include notebook {}


/* Medium Devices, Desktops @media only screen and (max-width: 992px) */
@include medium {}


/* Small Devices, Tablets @media only screen and (max-width: 768px) */
@include tablet {}


/* Extra Small Devices, Phones @media only screen and (max-width: 480px) */
@include phone {

    .logo-link {
        position: relative;
        margin: 3rem auto 0;
        width: 5rem;
        display: block;
        top: auto;
        left: auto;

        .logo {
            width: 100%;
            max-width: none;
        }
    }

    .content {
        height: auto;
        display: block;
        padding: 3rem 0 5rem;

        .content-box {
            padding: 0 1rem;

            .big-content {
                @include transform(scale(0.8));
                margin: 0 auto;
            }
        }
    }

    h1 {
        font-size: 2rem;
        line-height: 1.5;
    }

    footer {
        position: relative;
        padding-bottom: 5rem;

        ul {

            li {
                width: 100%;
                margin-bottom: 1rem;

                &::after {
                    display: none;
                }

                a {
                    padding: 0;

                    &::after {
                        display: none;
                    }
                }
            }
        }
    }

    #particles-js {
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
    }
}


/* Only for tablet in landscape mode @media only screen and (max-device-width: 1024px) and (orientation: landscape) */
@include landscape-tablet {}


/* Only for phone in landscape mode @media screen and (max-device-width: 667px) and (orientation: landscape) */
@include landscape-phone {

    .content {
        height: auto;
        display: block;
        padding: 5rem 0 5rem;

        .content-box {
            padding: 0 1rem;

            .big-content {
                @include transform(scale(0.8));
                margin: 0 auto;
            }
        }
    }

    footer {
        position: relative;
        padding-bottom: 5rem;

        ul {

            li {
                width: 100%;
                margin-bottom: 1rem;

                &::after {
                    display: none;
                }

                a {
                    padding: 0;

                    &::after {
                        display: none;
                    }
                }
            }
        }
    }

    #particles-js {
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
    }
}