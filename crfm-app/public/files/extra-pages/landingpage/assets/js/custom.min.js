jQuery(function(a){"use strict";var e=a("#main").offset().top;a(window).on("scroll",function(){stop=Math.round(a(window).scrollTop()),stop>e?(a(".navbar").addClass("past-main"),a(".navbar").addClass("effect-main")):a(".navbar").removeClass("past-main")}),a(document).on("click.nav",".navbar-collapse.in",function(e){a(e.target).is("a")&&a(this).removeClass("in").addClass("collapse")}),a(".testimonials").owlCarousel({slideSpeed:200,items:1,singleItem:!0,autoPlay:!0,pagination:!1}),a(".clients").owlCarousel({slideSpeed:200,items:5,singleItem:!1,autoPlay:!0,pagination:!1}),a(function(){a("a.page-scroll").bind("click",function(e){var n=a(this);a("html, body").stop().animate({scrollTop:a(n.attr("href")).offset().top},1500,"easeInOutExpo"),e.preventDefault()})}),a(".popup").magnificPopup({disableOn:0,type:"iframe",mainClass:"mfp-fade",removalDelay:160,preloader:!1,fixedContentPos:!1}),a(".jarallax").jarallax({speed:.7}),a(".personal-jarallax").jarallax({speed:.7}),a(window).load(function(){setTimeout(function(){a("#loading").fadeOut("slow",function(){})},3e3)}),(new WOW).init(),a(".counter").counterUp({delay:10,time:1e3}),a.find("#countdown")[0]&&(a("#countdown").countDown({targetDate:{day:14,month:7,year:2017,hour:11,min:13,sec:0},omitWeeks:!0}),"0"==a(".day_field .top").html()&&a(".day_field").css("display","none")),a(window).scroll(function(){a(this).scrollTop()>1e3?a("#back-top").fadeIn():a("#back-top").fadeOut()}),a("#back-top").on("click",function(){return a("#back-top").tooltip("hide"),a("body,html").animate({scrollTop:0},1500),!1}),a(".animsition").animsition({inClass:"fade-in",outClass:"fade-out",inDuration:1500,outDuration:800,linkElement:".animsition-link",loading:!0,loadingParentElement:"body",loadingClass:"animsition-loading",loadingInner:"",timeout:!1,timeoutCountdown:5e3,onLoadEvent:!0,browser:["animation-duration","-webkit-animation-duration"],overlay:!1,overlayClass:"animsition-overlay-slide",overlayParentElement:"body",transition:function(a){window.location.href=a}}),a(".subscribe-form").submit(function(e){e.preventDefault();var n=a(".subscribe-form").serialize();a.ajax({type:"POST",url:"assets/php/subscribe.php",data:n,dataType:"json",success:function(e){0==e.valid?(a(".success-message").hide(),a(".error-message").hide(),a(".error-message").html(e.message),a(".error-message").fadeIn("fast",function(){a(".subscribe-form").addClass("animated flash").one("webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend",function(){a(this).removeClass("animated flash")})})):(a(".error-message").hide(),a(".success-message").hide(),a(".subscribe-form").hide(),a(".success-message").html(e.message),a(".success-message").fadeIn("fast",function(){a(".top-content").backstretch("resize")}))}})})});