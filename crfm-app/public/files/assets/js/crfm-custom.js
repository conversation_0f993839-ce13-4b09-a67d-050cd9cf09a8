/**
 * CRFM Custom JavaScript
 * Fonctionnalités personnalisées pour l'application CRFM
 */

$(document).ready(function() {
    
    // Animation des cartes au chargement
    animateCards();
    
    // Mise à jour de l'heure en temps réel
    updateTime();
    setInterval(updateTime, 1000);
    
    // Gestion des tooltips
    initTooltips();
    
    // Gestion des confirmations de suppression
    initDeleteConfirmations();
    
    // Auto-refresh des statistiques
    if (window.location.pathname === '/dashboard' || window.location.pathname === '/') {
        setInterval(refreshDashboardStats, 300000); // 5 minutes
    }
    
});

/**
 * Animation des cartes au chargement
 */
function animateCards() {
    $('.card').each(function(index) {
        $(this).css({
            'opacity': '0',
            'transform': 'translateY(20px)'
        });
        
        setTimeout(() => {
            $(this).animate({
                'opacity': '1',
                'transform': 'translateY(0)'
            }, 500);
        }, index * 100);
    });
}

/**
 * Mise à jour de l'heure
 */
function updateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('fr-FR');
    const dateString = now.toLocaleDateString('fr-FR');
    
    $('#current-time').text(timeString);
    $('#current-date').text(dateString);
}

/**
 * Initialisation des tooltips
 */
function initTooltips() {
    $('[data-toggle="tooltip"]').tooltip();
    
    // Tooltips personnalisés pour les boutons d'action
    $('.btn[data-action]').each(function() {
        const action = $(this).data('action');
        let title = '';
        
        switch(action) {
            case 'view': title = 'Voir les détails'; break;
            case 'edit': title = 'Modifier'; break;
            case 'delete': title = 'Supprimer'; break;
            case 'validate': title = 'Valider'; break;
            case 'reject': title = 'Rejeter'; break;
            case 'print': title = 'Imprimer'; break;
            case 'download': title = 'Télécharger'; break;
        }
        
        if (title) {
            $(this).attr('title', title).tooltip();
        }
    });
}

/**
 * Gestion des confirmations de suppression
 */
function initDeleteConfirmations() {
    $(document).on('click', '.btn-delete, .btn[data-action="delete"]', function(e) {
        e.preventDefault();
        
        const item = $(this).data('item') || 'cet élément';
        
        if (confirm(`Êtes-vous sûr de vouloir supprimer ${item} ? Cette action est irréversible.`)) {
            // Si c'est un formulaire, le soumettre
            const form = $(this).closest('form');
            if (form.length) {
                form.submit();
            } else {
                // Sinon, rediriger vers l'URL
                const url = $(this).attr('href') || $(this).data('url');
                if (url) {
                    window.location.href = url;
                }
            }
        }
    });
}

/**
 * Refresh des statistiques du dashboard
 */
function refreshDashboardStats() {
    $.ajax({
        url: '/api/dashboard/stats',
        method: 'GET',
        success: function(data) {
            if (data.success) {
                updateDashboardCards(data.data);
            }
        },
        error: function() {
            console.log('Erreur lors du refresh des statistiques');
        }
    });
}

/**
 * Mise à jour des cartes du dashboard
 */
function updateDashboardCards(stats) {
    // Mise à jour des valeurs avec animation
    $('.stat-value').each(function() {
        const $this = $(this);
        const newValue = stats[$this.data('stat')];
        
        if (newValue !== undefined) {
            animateValue($this, parseInt($this.text().replace(/\D/g, '')), newValue);
        }
    });
}

/**
 * Animation des valeurs numériques
 */
function animateValue(element, start, end) {
    const duration = 1000;
    const startTime = performance.now();
    
    function update(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        const current = Math.floor(start + (end - start) * progress);
        element.text(current.toLocaleString());
        
        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }
    
    requestAnimationFrame(update);
}

/**
 * Gestion des notifications toast
 */
function showToast(message, type = 'info') {
    const toastHtml = `
        <div class="toast toast-${type}" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header">
                <i class="feather icon-${getToastIcon(type)} me-2"></i>
                <strong class="me-auto">CRFM</strong>
                <small>À l'instant</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;
    
    // Ajouter le toast au container
    let toastContainer = $('#toast-container');
    if (!toastContainer.length) {
        toastContainer = $('<div id="toast-container" class="position-fixed top-0 end-0 p-3" style="z-index: 9999;"></div>');
        $('body').append(toastContainer);
    }
    
    const $toast = $(toastHtml);
    toastContainer.append($toast);
    
    // Initialiser et afficher le toast
    const toast = new bootstrap.Toast($toast[0]);
    toast.show();
    
    // Supprimer le toast après fermeture
    $toast.on('hidden.bs.toast', function() {
        $(this).remove();
    });
}

/**
 * Icône pour les toasts selon le type
 */
function getToastIcon(type) {
    switch(type) {
        case 'success': return 'check-circle';
        case 'error': return 'alert-circle';
        case 'warning': return 'alert-triangle';
        default: return 'info';
    }
}

/**
 * Formatage des nombres
 */
function formatNumber(num) {
    return new Intl.NumberFormat('fr-FR').format(num);
}

/**
 * Formatage des montants en FCFA
 */
function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'XOF',
        minimumFractionDigits: 0
    }).format(amount).replace('XOF', 'FCFA');
}

/**
 * Validation des formulaires
 */
function initFormValidation() {
    $('.needs-validation').on('submit', function(e) {
        if (!this.checkValidity()) {
            e.preventDefault();
            e.stopPropagation();
            showToast('Veuillez corriger les erreurs dans le formulaire', 'error');
        }
        
        $(this).addClass('was-validated');
    });
}

/**
 * Gestion des modales
 */
function initModals() {
    // Auto-focus sur le premier champ des modales
    $('.modal').on('shown.bs.modal', function() {
        $(this).find('input, select, textarea').first().focus();
    });
    
    // Reset des formulaires à la fermeture des modales
    $('.modal').on('hidden.bs.modal', function() {
        $(this).find('form')[0]?.reset();
        $(this).find('.was-validated').removeClass('was-validated');
    });
}

/**
 * Recherche en temps réel
 */
function initLiveSearch() {
    $('.live-search').on('input', function() {
        const query = $(this).val().toLowerCase();
        const target = $(this).data('target');
        
        $(target + ' tbody tr').each(function() {
            const text = $(this).text().toLowerCase();
            $(this).toggle(text.includes(query));
        });
    });
}

/**
 * Export de données
 */
function exportData(format, data, filename) {
    if (format === 'csv') {
        exportToCSV(data, filename);
    } else if (format === 'excel') {
        exportToExcel(data, filename);
    }
}

/**
 * Export CSV
 */
function exportToCSV(data, filename) {
    const csv = convertToCSV(data);
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = filename + '.csv';
    a.click();
    
    window.URL.revokeObjectURL(url);
}

/**
 * Conversion en CSV
 */
function convertToCSV(data) {
    if (!data.length) return '';
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(';'),
        ...data.map(row => headers.map(header => row[header]).join(';'))
    ].join('\n');
    
    return '\ufeff' + csvContent; // BOM pour UTF-8
}

// Fonctions utilitaires globales
window.CRFM = {
    showToast,
    formatNumber,
    formatCurrency,
    exportData
};
