<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    if (auth()->check()) {
        return redirect()->route('dashboard.index');
    }
    return redirect()->route('auth.login');
});

// Routes pour les pages principales (temporaires - seront remplacées par les modules)
Route::middleware(['web', 'auth'])->group(function () {
    // Routes temporaires pour les liens du menu
    Route::get('/affiliation', function () {
        return redirect()->route('adherents.index');
    })->name('affiliation.index');

    Route::get('/declaration', function () {
        return view('pages.declaration');
    })->name('declaration.index');

    Route::get('/pre-liquidation', function () {
        return view('pages.pre-liquidation');
    })->name('pre-liquidation.index');

    Route::get('/statistiques', function () {
        return view('pages.statistiques');
    })->name('statistiques.index');

    Route::get('/parametres', function () {
        return view('pages.parametres');
    })->name('parametres.index');

    Route::get('/outils', function () {
        return view('pages.outils');
    })->name('outils.index');

    // API Routes
    Route::get('/api/dashboard/stats', function () {
        return response()->json([
            'success' => true,
            'data' => [
                'total_adherents' => rand(8700, 8800),
                'cotisations_mois' => rand(15000000, 16000000),
                'pensions_en_cours' => rand(40, 50),
                'utilisateurs_actifs' => rand(5, 10)
            ]
        ]);
    })->name('api.dashboard.stats');

    // API pour l'historique des cotisations d'un adhérent
    Route::get('/api/adherents/{adherent}/cotisations-history', function ($adherentId) {
        // Simulation de données - à remplacer par la vraie logique
        return response()->json([
            [
                'periode' => 'Jan 2024',
                'montant' => '45,000',
                'status' => 'Payée',
                'status_color' => 'success'
            ],
            [
                'periode' => 'Déc 2023',
                'montant' => '45,000',
                'status' => 'Payée',
                'status_color' => 'success'
            ],
            [
                'periode' => 'Nov 2023',
                'montant' => '42,000',
                'status' => 'En retard',
                'status_color' => 'danger'
            ]
        ]);
    })->name('api.adherents.cotisations-history');
});
