# phpunit/php-invoker

[![Latest Stable Version](https://poser.pugx.org/phpunit/php-invoker/v/stable.png)](https://packagist.org/packages/phpunit/php-invoker)
[![CI Status](https://github.com/sebastian<PERSON>mann/php-invoker/workflows/CI/badge.svg)](https://github.com/sebastian<PERSON>mann/php-invoker/actions)
[![Type Coverage](https://shepherd.dev/github/sebastian<PERSON>mann/php-invoker/coverage.svg)](https://shepherd.dev/github/sebastian<PERSON>mann/php-invoker)
[![codecov](https://codecov.io/gh/sebastian<PERSON>mann/php-invoker/branch/main/graph/badge.svg)](https://codecov.io/gh/sebastianbergmann/php-invoker)

## Installation

You can add this library as a local, per-project dependency to your project using [Composer](https://getcomposer.org/):

```
composer require phpunit/php-invoker
```

If you only need this library during development, for instance to run your project's test suite, then you should add it as a development-time dependency:

```
composer require --dev phpunit/php-invoker
```
