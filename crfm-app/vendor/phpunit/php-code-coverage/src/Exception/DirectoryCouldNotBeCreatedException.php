<?php declare(strict_types=1);
/*
 * This file is part of phpunit/php-code-coverage.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace <PERSON><PERSON><PERSON><PERSON><PERSON>\CodeCoverage\Util;

use RuntimeException;
use <PERSON><PERSON><PERSON><PERSON>n\CodeCoverage\Exception;

final class DirectoryCouldNotBeCreatedException extends RuntimeException implements Exception
{
}
